package com.senox.realty.service;

import com.senox.common.constant.BillType;
import com.senox.common.domain.PenaltySetting;
import com.senox.common.service.PenaltySettingService;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.RealtyBillVo;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/27 16:11
 */
@AllArgsConstructor
@Service
public class RealtyBillPenaltyService {
    private static final Logger logger = LoggerFactory.getLogger(RealtyBillPenaltyService.class);

    private final RealtyBillService realtyBillService;
    private final PenaltySettingService penaltySettingService;
    private final ContractService contractService;

    /**
     * 异步计算应收账单滞纳金
     */
    @Async
    public void calBillPenaltyAsync() {
        calBillPenalty(null);
    }

    /**
     * 计算应收账单的滞纳金
     * @param contractNo
     */
    public void calBillPenalty(String contractNo) {
        // 页号
        int pageIndex = 1;
        // 物业账单
        List<RealtyBillVo> billList = listUnpaidRealtyBill(pageIndex, RealtyConst.BATCH_SIZE_5000, contractNo);

        Map<String, PenaltySetting> penaltyConfig = new HashMap<>(10);
        while (!CollectionUtils.isEmpty(billList)) {
            calBillPenalty(billList, penaltyConfig);

            pageIndex++;
            billList = listUnpaidRealtyBill(pageIndex, RealtyConst.BATCH_SIZE_5000, contractNo);
        }
    }

    /**
     * 计算应收账单的滞纳金
     * @param list
     * @param configMap
     */
    private void calBillPenalty(List<RealtyBillVo> list, Map<String, PenaltySetting> configMap) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (RealtyBillVo item : list) {
            // penalty date
            PenaltySetting setting = getPenaltyConfig(item, configMap);

            // ignore paid bills and penalty ignore bills
            if (isBillPenaltyFree(item) || isSettingPenaltyFree(setting)) {
                continue;
            }

            try {
                // 未支付本金
                BigDecimal amount = item.getAmount();
                BigDecimal penalty = PenaltySetting.calPenalty(amount, setting.getPenaltyCalDate(), item.getPenaltyRate() == null ? RealtyConst.DF_PENALTY_RATE : item.getPenaltyRate());
                if (!DecimalUtils.isNegative(penalty)) {
                    saveBillPenalty(item, penalty);
                    logger.info("物业账单 {} - {}{} 滞纳金 {}", item.getContractNo(), item.getBillYear(),
                            StringUtils.fixLength(String.valueOf(item.getBillMonth()), 2, '0'), penalty);
                }

            } catch (Exception e) {
                logger.error("计算账单 " + JsonUtils.object2Json(item) +" 滞纳金出错", e);
            }

        }
    }



    /**
     * 未支付账单
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<RealtyBillVo> listUnpaidRealtyBill(int pageNo, int pageSize, String contractNo) {
        RealtyBillSearchVo search = new RealtyBillSearchVo();
        search.setStatus(BillStatus.INIT.getStatus());
        search.setPageNo(pageNo);
        search.setPageSize(pageSize);

        if (!StringUtils.isBlank(contractNo)) {
            search.setContractNo(contractNo);
        }

        return realtyBillService.listRealtyBillWithPenaltyRate(search);
    }

    /**
     * 获取应收账单滞纳金日期
     * @param bill
     * @param penaltyConfig
     * @return
     */
    private PenaltySetting getPenaltyConfig(RealtyBillVo bill, Map<String, PenaltySetting> penaltyConfig) {
        PenaltySetting result = getCustomizePenaltyConfig(bill.getBillYear(), bill.getBillMonth(), penaltyConfig);
        if (result == null) {
            result = new PenaltySetting(StringUtils.buildYearMonthStr(bill.getBillYear(), bill.getBillMonth()),
                    BillType.REALTY.getValue(), bill.getPenaltyDate());
        }
        // 未配置日期，按默认来
        if (result.getPenaltyStartDate() == null) {
            result.setPenaltyStartDate(bill.getPenaltyDate());
        }
        if (result.getPenaltyCalDate() == null) {
            result.setPenaltyCalDate(bill.getPenaltyDate());
        }
        return result;
    }

    /**
     * 获取账单月滞纳金日期
     * @param year
     * @param month
     * @param configs
     * @return
     */
    private PenaltySetting getCustomizePenaltyConfig(Integer year, Integer month, Map<String, PenaltySetting> configs) {
        // customize penaltyDate
        String yearMonth = StringUtils.buildYearMonthStr(year, month);
        if (configs.containsKey(yearMonth)) {
            return configs.get(yearMonth);
        }

        PenaltySetting result = penaltySettingService.findByYearMonth(yearMonth, BillType.REALTY);
        if (!PenaltySetting.checkCustomizedPenalty(result)) {
            result = null;
        } else {
            logger.info("{} customized {} penalty setting {}", yearMonth, BillType.REALTY, JsonUtils.object2Json(result));
        }
        configs.put(yearMonth, result);

        return result;
    }

    /**
     * 是否免滞纳金账单
     * @param bill
     * @return
     */
    private boolean isBillPenaltyFree(RealtyBillVo bill) {
        return isBillPaid(bill) || isBillBankOffer(bill) || isContractSuspended(bill);
    }


    /**
     * 判断账单对应的合同是否已停用
     * @param bill
     * @return
     */
    private boolean isContractSuspended(RealtyBillVo bill) {
        // 通过合同号查询合同状态
        ContractVo contract = contractService.findWithExtByContractNo(bill.getContractNo());
        if (contract != null) {
            ContractStatus status = ContractStatus.fromValue(contract.getStatus());
            return ContractStatus.SUSPEND == status;
        }
        return false;
    }

    /**
     * 是否全局设置免滞纳金
     * @param setting
     * @return
     */
    private boolean isSettingPenaltyFree(PenaltySetting setting) {
        LocalDate nowDate = LocalDate.now();
        return setting.checkPenaltyFree() || !nowDate.isAfter(setting.getPenaltyStartDate()) || !nowDate.isAfter(setting.getPenaltyCalDate());
    }

    /**
     * 应付账单已支付
     * @param bill
     * @return
     */
    private boolean isBillPaid(RealtyBillVo bill) {
        return BillStatus.PAID.getStatus() == bill.getStatus();
    }

    /**
     * 应付账单报盘未回盘
     * @param bill
     * @return
     */
    private boolean isBillBankOffer(RealtyBillVo bill) {
        boolean result  = BooleanUtils.isTrue(bill.getOffer()) && !BooleanUtils.isTrue(bill.getBack());
        if (result) {
            logger.info("{} 账单报盘未回盘，暂不计算滞纳金。", JsonUtils.object2Json(bill));
        }
        return result;
    }


    /**
     * 保存物业滞纳金
     * @param billVo
     * @param penalty
     */
    private void saveBillPenalty(RealtyBillVo billVo, BigDecimal penalty) {
        RealtyBill bill = new RealtyBill();
        bill.setId(billVo.getId());
        bill.setPenaltyAmount(penalty);
        bill.setTotalAmount(DecimalUtils.add(billVo.getAmount(), penalty));
        bill.setPaidStillAmount(DecimalUtils.subtract(bill.getTotalAmount(), bill.getPaidAmount(), bill.getPenaltyIgnoreAmount()));
        realtyBillService.updateBill(bill);
    }
}

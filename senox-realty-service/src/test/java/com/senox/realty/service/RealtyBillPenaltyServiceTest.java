package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.RealtyBillVo;
import com.senox.realty.vo.RealtyContractSuspendDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 滞纳金功能测试类
 * 测试合同停用后滞纳金不再计算的功能
 */
@Slf4j
public class RealtyBillPenaltyServiceTest extends BaseTest {

    @Autowired
    private RealtyBillPenaltyService penaltyService;
    
    @Autowired
    private RealtyBillService billService;
    
    @Autowired
    private ContractService contractService;
    
    @Autowired
    private ContractBillService contractBillService;

    /**
     * 测试合同停用后滞纳金不再计算
     */
    @Test
    public void testPenaltyNotCalculatedAfterContractSuspended() {
        log.info("=== 开始测试：合同停用后滞纳金不再计算 ===");
        
        // 1. 查找测试数据
        String testContractNo = findTestContract();
        if (testContractNo == null) {
            log.warn("未找到合适的测试合同，跳过测试");
            return;
        }
        
        RealtyBillVo testBill = findTestBill(testContractNo);
        if (testBill == null) {
            log.warn("未找到合适的测试账单，跳过测试");
            return;
        }
        
        log.info("找到测试数据 - 合同号: {}, 账单ID: {}", testContractNo, testBill.getId());
        
        // 2. 记录修改前的滞纳金
        BigDecimal penaltyBeforeSuspend = testBill.getPenaltyAmount();
        log.info("合同停用前滞纳金: {}", penaltyBeforeSuspend);
        
        // 3. 触发滞纳金计算（修改前）
        penaltyService.calBillPenalty(testContractNo);
        
        // 4. 查看计算后的滞纳金
        RealtyBillVo billAfterCalc = billService.findDetailById(testBill.getId());
        BigDecimal penaltyAfterCalc = billAfterCalc.getPenaltyAmount();
        log.info("触发计算后滞纳金: {}", penaltyAfterCalc);
        
        // 5. 停用合同
        suspendContract(testContractNo);
        log.info("合同已停用: {}", testContractNo);
        
        // 6. 再次触发滞纳金计算（修改后）
        penaltyService.calBillPenalty(testContractNo);
        
        // 7. 查看停用后的滞纳金
        RealtyBillVo billAfterSuspend = billService.findDetailById(testBill.getId());
        BigDecimal penaltyAfterSuspend = billAfterSuspend.getPenaltyAmount();
        log.info("合同停用后滞纳金: {}", penaltyAfterSuspend);
        
        // 8. 验证结果
        log.info("=== 测试结果 ===");
        log.info("停用前滞纳金: {}", penaltyAfterCalc);
        log.info("停用后滞纳金: {}", penaltyAfterSuspend);
        
        if (penaltyAfterCalc.compareTo(penaltyAfterSuspend) == 0) {
            log.info("✅ 测试通过：合同停用后滞纳金未增加");
        } else {
            log.error("❌ 测试失败：合同停用后滞纳金仍在增加");
        }
        
        log.info("=== 测试完成 ===");
    }
    
    /**
     * 查找合适的测试合同
     */
    private String findTestContract() {
        try {
            // 查找启用状态的租赁或物业合同
            List<Contract> contracts = contractService.listAll();
            
            for (Contract contract : contracts) {
                if (ContractStatus.fromValue(contract.getStatus()) == ContractStatus.EFFECTIVE) {
                    ContractType type = ContractType.fromValue(contract.getType());
                    if (type == ContractType.LEASE || type == ContractType.ESTATE) {
                        log.info("找到测试合同: {} (类型: {})", contract.getContractNo(), type.getChName());
                        return contract.getContractNo();
                    }
                }
            }
        } catch (Exception e) {
            log.error("查找测试合同失败", e);
        }
        return null;
    }
    
    /**
     * 查找合适的测试账单
     */
    private RealtyBillVo findTestBill(String contractNo) {
        try {
            RealtyBillSearchVo search = new RealtyBillSearchVo();
            search.setContractNo(contractNo);
            search.setStatus(BillStatus.INIT.getStatus()); // 未支付状态
            search.setPageNo(1);
            search.setPageSize(5);
            
            List<RealtyBillVo> bills = billService.listRealtyBillWithDetail(search);
            
            if (!CollectionUtils.isEmpty(bills)) {
                RealtyBillVo bill = bills.get(0);
                log.info("找到测试账单: {} ({}年{}月)", bill.getId(), bill.getBillYear(), bill.getBillMonth());
                return bill;
            }
        } catch (Exception e) {
            log.error("查找测试账单失败", e);
        }
        return null;
    }
    
    /**
     * 停用合同
     */
    private void suspendContract(String contractNo) {
        try {
            RealtyContractSuspendDto suspendDto = new RealtyContractSuspendDto();
            suspendDto.setContractNo(contractNo);
            suspendDto.setSuspendDate(LocalDate.now());
            
            contractService.suspendContract(suspendDto);
            log.info("合同停用成功: {}", contractNo);
        } catch (Exception e) {
            log.error("停用合同失败: {}", contractNo, e);
        }
    }
    
    /**
     * 简单的数据查看测试
     */
    @Test
    public void testViewTestData() {
        log.info("=== 查看测试数据 ===");
        
        // 查看合同数据
        try {
            List<Contract> contracts = contractService.listAll();
            log.info("系统中共有 {} 个合同", contracts.size());
            
            int effectiveCount = 0;
            for (Contract contract : contracts) {
                if (ContractStatus.fromValue(contract.getStatus()) == ContractStatus.EFFECTIVE) {
                    ContractType type = ContractType.fromValue(contract.getType());
                    if (type == ContractType.LEASE || type == ContractType.ESTATE) {
                        effectiveCount++;
                        log.info("启用合同: {} (类型: {}, 开始: {}, 结束: {})", 
                            contract.getContractNo(), type.getChName(), 
                            contract.getStartDate(), contract.getEndDate());
                        
                        if (effectiveCount >= 3) break; // 只显示前3个
                    }
                }
            }
        } catch (Exception e) {
            log.error("查看合同数据失败", e);
        }
        
        // 查看账单数据
        try {
            RealtyBillSearchVo search = new RealtyBillSearchVo();
            search.setStatus(BillStatus.INIT.getStatus());
            search.setPageNo(1);
            search.setPageSize(5);
            
            List<RealtyBillVo> bills = billService.listRealtyBillWithDetail(search);
            log.info("系统中共有 {} 个未支付账单", bills.size());
            
            for (RealtyBillVo bill : bills) {
                log.info("未支付账单: {} (合同: {}, {}年{}月, 滞纳金: {})", 
                    bill.getId(), bill.getContractNo(), 
                    bill.getBillYear(), bill.getBillMonth(), 
                    bill.getPenaltyAmount());
            }
        } catch (Exception e) {
            log.error("查看账单数据失败", e);
        }
        
        log.info("=== 数据查看完成 ===");
    }
}

package com.senox.realty.service;

import com.senox.context.AdminUserDto;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 滞纳金功能测试类
 * 测试合同停用后滞纳金不再计算的功能
 */
@Slf4j
public class RealtyBillPenaltyServiceTest extends BaseTest {

    @Autowired
    private RealtyBillPenaltyService penaltyService;
    
    @Autowired
    private RealtyBillService billService;
    
    @Autowired
    private ContractService contractService;
    
    @Autowired
    private ContractBillService contractBillService;

    /**
     * 测试合同停用后滞纳金不再计算
     */
    @Test
    public void testPenaltyNotCalculatedAfterContractSuspended() {
        log.info("=== 开始测试：合同停用后滞纳金不再计算 ===");
        
        // 1. 查找测试数据
        String testContractNo = findTestContract();
        if (testContractNo == null) {
            log.warn("未找到合适的测试合同，跳过测试");
            return;
        }
        
        RealtyBillVo testBill = findTestBill(testContractNo);
        if (testBill == null) {
            log.warn("未找到合适的测试账单，跳过测试");
            return;
        }
        
        log.info("找到测试数据 - 合同号: {}, 账单ID: {}", testContractNo, testBill.getId());
        
        // 2. 记录修改前的滞纳金
        BigDecimal penaltyBeforeSuspend = testBill.getPenaltyAmount();
        log.info("合同停用前滞纳金: {}", penaltyBeforeSuspend);
        
        // 3. 触发滞纳金计算（修改前）
        penaltyService.calBillPenalty(testContractNo);
        
        // 4. 查看计算后的滞纳金
        RealtyBillVo billAfterCalc = billService.findDetailById(testBill.getId());
        BigDecimal penaltyAfterCalc = billAfterCalc.getPenaltyAmount();
        log.info("触发计算后滞纳金: {}", penaltyAfterCalc);
        
        // 5. 停用合同
        suspendContract(testContractNo);
        log.info("合同已停用: {}", testContractNo);
        
        // 6. 再次触发滞纳金计算（修改后）
        penaltyService.calBillPenalty(testContractNo);
        
        // 7. 查看停用后的滞纳金
        RealtyBillVo billAfterSuspend = billService.findDetailById(testBill.getId());
        BigDecimal penaltyAfterSuspend = billAfterSuspend.getPenaltyAmount();
        log.info("合同停用后滞纳金: {}", penaltyAfterSuspend);
        
        // 8. 验证结果
        log.info("=== 测试结果 ===");
        log.info("停用前滞纳金: {}", penaltyAfterCalc);
        log.info("停用后滞纳金: {}", penaltyAfterSuspend);
        
        if (penaltyAfterCalc.compareTo(penaltyAfterSuspend) == 0) {
            log.info("测试通过：合同停用后滞纳金未增加");
        } else {
            log.error("测试失败：合同停用后滞纳金仍在增加");
        }
        
        log.info("=== 测试完成 ===");
    }
    
    /**
     * 查找合适的测试合同
     */
    private String findTestContract() {
        try {
            // 直接使用已知有滞纳金的合同
            String knownContractNo = "2010111000022";
            ContractVo contract = contractService.findWithExtByContractNo(knownContractNo);

            if (contract != null && ContractStatus.fromValue(contract.getStatus()) == ContractStatus.EFFECTIVE) {
                log.info("找到测试合同: {} (状态: 启用)", contract.getContractNo());
                return contract.getContractNo();
            }

            // 如果已知合同不可用，则查找其他合同
            ContractSearchVo search = new ContractSearchVo();
            search.setStatus(ContractStatus.EFFECTIVE.ordinal());
            search.setPageNo(1);
            search.setPageSize(10);

            List<ContractVo> contracts = contractService.listContract(search);

            for (ContractVo c : contracts) {
                // 不再检查合同类型，只要是启用状态的合同都可以测试
                log.info("找到测试合同: {} (状态: 启用)", c.getContractNo());
                return c.getContractNo();
            }
        } catch (Exception e) {
            log.error("查找测试合同失败", e);
        }
        return null;
    }
    
    /**
     * 查找合适的测试账单
     */
    private RealtyBillVo findTestBill(String contractNo) {
        try {
            RealtyBillSearchVo search = new RealtyBillSearchVo();
            search.setContractNo(contractNo);
            search.setStatus(BillStatus.INIT.getStatus()); // 未支付状态
            search.setPageNo(1);
            search.setPageSize(5);
            
            List<RealtyBillVo> bills = billService.listRealtyBillWithDetail(search);
            
            if (!CollectionUtils.isEmpty(bills)) {
                RealtyBillVo bill = bills.get(0);
                log.info("找到测试账单: {} ({}年{}月)", bill.getId(), bill.getBillYear(), bill.getBillMonth());
                return bill;
            }
        } catch (Exception e) {
            log.error("查找测试账单失败", e);
        }
        return null;
    }
    
    /**
     * 停用合同
     */
    private void suspendContract(String contractNo) {
        try {
            RealtyContractSuspendDto suspendDto = new RealtyContractSuspendDto();
            suspendDto.setContractNo(contractNo);
            suspendDto.setSuspendDate(LocalDate.now());

            // 创建操作者信息，避免 modifier_id 为空的错误
            AdminUserDto operator = new AdminUserDto();
            operator.setUserId(1L);
            operator.setUsername("测试用户");
            operator.setToken("test-token");
            suspendDto.setOperator(operator);

            contractService.suspendContract(suspendDto);
            log.info("合同停用成功: {}", contractNo);
        } catch (Exception e) {
            log.error("停用合同失败: {}", contractNo, e);
        }
    }
    
    /**
     * 直接测试已知数据的滞纳金功能
     */
    @Test
    public void testKnownDataPenalty() {
        log.info("=== 开始测试已知数据的滞纳金功能 ===");

        try {
            // 1. 动态查找测试账单（只查1条，减少数据量）
            RealtyBillSearchVo search = new RealtyBillSearchVo();
            search.setStatus(BillStatus.INIT.getStatus());
            search.setPageNo(1);
            search.setPageSize(1); // 只查1条

            List<RealtyBillVo> bills = billService.listRealtyBillWithDetail(search);
            if (CollectionUtils.isEmpty(bills)) {
                log.warn("未找到未支付账单，跳过测试");
                return;
            }

            RealtyBillVo bill = bills.get(0);
            String testContractNo = bill.getContractNo();
            Long testBillId = bill.getId();

            log.info("找到测试账单: {} (合同: {})", testBillId, testContractNo);

            // 移除详细日志，减少输出

            // 2. 记录停用前的滞纳金
            BigDecimal penaltyBefore = bill.getPenaltyAmount();

            // 3. 触发滞纳金计算（停用前）
            penaltyService.calBillPenalty(testContractNo);

            // 4. 查看计算后的滞纳金
            RealtyBillVo billAfterCalc = billService.findDetailById(testBillId);
            BigDecimal penaltyAfterCalc = billAfterCalc.getPenaltyAmount();
            log.info("停用前触发计算后滞纳金: {}", penaltyAfterCalc);

            // 5. 停用合同
            suspendContract(testContractNo);
            log.info("合同已停用: {}", testContractNo);

            // 6. 再次触发滞纳金计算（停用后）
            penaltyService.calBillPenalty(testContractNo);

            // 7. 查看停用后的滞纳金
            RealtyBillVo billAfterSuspend = billService.findDetailById(testBillId);
            BigDecimal penaltyAfterSuspend = billAfterSuspend.getPenaltyAmount();
            log.info("合同停用后滞纳金: {}", penaltyAfterSuspend);

            // 8. 验证结果
            log.info("=== 测试结果 ===");
            log.info("停用前滞纳金: {}", penaltyAfterCalc);
            log.info("停用后滞纳金: {}", penaltyAfterSuspend);

            if (penaltyAfterCalc.compareTo(penaltyAfterSuspend) == 0) {
                log.info(" 测试通过：合同停用后滞纳金未增加");
            } else {
                log.error(" 测试失败：合同停用后滞纳金仍在增加");
            }

        } catch (Exception e) {
            log.error("测试失败", e);
        }

        log.info("=== 测试完成 ===");
    }

    /**
     * 查看系统中的测试数据
     */
    @Test
    public void testViewTestData() {
        log.info("=== 查看系统测试数据 ===");

        // 查看启用合同数据
        try {
            ContractSearchVo search = new ContractSearchVo();
            search.setStatus(ContractStatus.EFFECTIVE.ordinal());
            search.setPageNo(1);
            search.setPageSize(10);

            List<ContractVo> contracts = contractService.listContract(search);
            log.info("系统中共有 {} 个启用合同", contracts.size());

            for (ContractVo contract : contracts) {
                log.info("启用合同: {} (开始: {}, 结束: {})",
                    contract.getContractNo(), contract.getStartDate(), contract.getEndDate());
            }
        } catch (Exception e) {
            log.error("查看合同数据失败", e);
        }

        // 查看未支付账单数据
        try {
            RealtyBillSearchVo search = new RealtyBillSearchVo();
            search.setStatus(BillStatus.INIT.getStatus());
            search.setPageNo(1);
            search.setPageSize(10);

            List<RealtyBillVo> bills = billService.listRealtyBillWithDetail(search);
            log.info("系统中共有 {} 个未支付账单", bills.size());

            for (RealtyBillVo bill : bills) {
                log.info("未支付账单: {} (合同: {}, {}年{}月, 滞纳金: {})",
                    bill.getId(), bill.getContractNo(),
                    bill.getBillYear(), bill.getBillMonth(),
                    bill.getPenaltyAmount());
            }
        } catch (Exception e) {
            log.error("查看账单数据失败", e);
        }

        // 查看停用合同数据
        try {
            ContractSearchVo search = new ContractSearchVo();
            search.setStatus(ContractStatus.SUSPEND.ordinal());
            search.setPageNo(1);
            search.setPageSize(5);

            List<ContractVo> contracts = contractService.listContract(search);
            log.info("系统中共有 {} 个停用合同", contracts.size());

            for (ContractVo contract : contracts) {
                log.info("停用合同: {} (停用日期: {})",
                    contract.getContractNo(), contract.getEndDate());
            }
        } catch (Exception e) {
            log.error("查看停用合同数据失败", e);
        }

        log.info("=== 数据查看完成 ===");
    }
}
